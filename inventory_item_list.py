#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
背包物品列表工具
根据已知背包基址 0x0068FA10 扫描并尝试列出物品（名称、数量、地址等）

说明:
- 按照 quick_monster_list.py 的风格实现，使用 ctypes 直读内存
- 兼容两类常见结构: 指针数组 → 物品结构；或 直接结构数组
- 物品结构的字段未知，使用启发式在若干常见偏移尝试解析 名称/数量/ID
- 名称优先按 GBK 解码，失败回退 UTF-8/ASCII
"""

import ctypes
import ctypes.wintypes
import struct


# ============================ 基础工具 ============================

def enable_debug_privilege():
    """启用调试权限"""
    try:
        token = ctypes.wintypes.HANDLE()
        advapi32 = ctypes.windll.advapi32
        kernel32 = ctypes.windll.kernel32

        if not advapi32.OpenProcessToken(
            kernel32.GetCurrentProcess(),
            0x00000020 | 0x00000008,
            ctypes.byref(token)
        ):
            return False

        class LUID(ctypes.Structure):
            _fields_ = [("LowPart", ctypes.wintypes.DWORD),
                        ("HighPart", ctypes.wintypes.LONG)]

        luid = LUID()
        if not advapi32.LookupPrivilegeValueW(None, "SeDebugPrivilege", ctypes.byref(luid)):
            return False

        class TOKEN_PRIVILEGES(ctypes.Structure):
            _fields_ = [("PrivilegeCount", ctypes.wintypes.DWORD),
                        ("Luid", LUID),
                        ("Attributes", ctypes.wintypes.DWORD)]

        tp = TOKEN_PRIVILEGES()
        tp.PrivilegeCount = 1
        tp.Luid = luid
        tp.Attributes = 0x00000002

        result = advapi32.AdjustTokenPrivileges(token, False, ctypes.byref(tp), 0, None, None)
        kernel32.CloseHandle(token)
        return result != 0
    except:  # noqa: E722
        return False


def get_process_handle(process_name: str = "mts.exe"):
    """获取进程句柄"""
    enable_debug_privilege()

    kernel32 = ctypes.windll.kernel32
    snapshot = kernel32.CreateToolhelp32Snapshot(0x2, 0)

    class PROCESSENTRY32(ctypes.Structure):
        _fields_ = [
            ("dwSize", ctypes.wintypes.DWORD),
            ("cntUsage", ctypes.wintypes.DWORD),
            ("th32ProcessID", ctypes.wintypes.DWORD),
            ("th32DefaultHeapID", ctypes.POINTER(ctypes.wintypes.ULONG)),
            ("th32ModuleID", ctypes.wintypes.DWORD),
            ("cntThreads", ctypes.wintypes.DWORD),
            ("th32ParentProcessID", ctypes.wintypes.DWORD),
            ("pcPriClassBase", ctypes.wintypes.LONG),
            ("dwFlags", ctypes.wintypes.DWORD),
            ("szExeFile", ctypes.c_char * 260)
        ]

    pe32 = PROCESSENTRY32()
    pe32.dwSize = ctypes.sizeof(PROCESSENTRY32)

    process_id = None
    if kernel32.Process32First(snapshot, ctypes.byref(pe32)):
        while True:
            if pe32.szExeFile.decode('utf-8', errors='ignore') == process_name:
                process_id = pe32.th32ProcessID
                break
            if not kernel32.Process32Next(snapshot, ctypes.byref(pe32)):
                break

    kernel32.CloseHandle(snapshot)

    if not process_id:
        return None, None

    handle = kernel32.OpenProcess(0x1F0FFF, False, process_id)
    return handle, process_id


def test_memory_access(handle, address: int):
    """测试内存可读性并返回4字节值"""
    try:
        buffer = ctypes.create_string_buffer(4)
        bytes_read = ctypes.wintypes.DWORD()
        success = ctypes.windll.kernel32.ReadProcessMemory(
            handle, ctypes.c_void_p(address), buffer, 4, ctypes.byref(bytes_read)
        )
        if success and bytes_read.value == 4:
            return True, struct.unpack('<I', buffer.raw)[0]
        return False, 0
    except:  # noqa: E722
        return False, 0


def read_int(handle, address: int) -> int:
    """读取4字节无符号整数"""
    ok, v = test_memory_access(handle, address)
    return v if ok else 0


def read_byte(handle, address: int) -> int:
    """读取1字节无符号整数"""
    try:
        buffer = ctypes.create_string_buffer(1)
        bytes_read = ctypes.wintypes.DWORD()
        success = ctypes.windll.kernel32.ReadProcessMemory(
            handle, ctypes.c_void_p(address), buffer, 1, ctypes.byref(bytes_read)
        )
        if success and bytes_read.value == 1:
            return buffer.raw[0]
        return 0
    except:  # noqa: E722
        return 0


def read_string_gbk(handle, address: int, max_length: int = 64) -> str:
    """读取以0结尾的 GBK/UTF-8/ASCII 字符串"""
    try:
        if address < 0x10000 or address > 0x7FFFFFFF:
            return ""
        buffer = ctypes.create_string_buffer(max_length)
        bytes_read = ctypes.wintypes.DWORD()
        success = ctypes.windll.kernel32.ReadProcessMemory(
            handle, ctypes.c_void_p(address), buffer, max_length, ctypes.byref(bytes_read)
        )
        if not success:
            return ""
        data = buffer.raw
        end_pos = data.find(b'\x00')
        if end_pos != -1:
            data = data[:end_pos]
        try:
            return data.decode('gbk', errors='ignore').strip()
        except:  # noqa: E722
            try:
                return data.decode('utf-8', errors='ignore').strip()
            except:  # noqa: E722
                return data.decode('ascii', errors='ignore').strip()
    except:  # noqa: E722
        return ""


def is_plausible_ptr(value: int) -> bool:
    """判断值是否可能是有效的堆/模块内指针"""
    return 0x01000000 <= value <= 0x7FFFFFFF


# ============================ 物品解析启发式 ============================

def try_extract_name_by_pointers(handle, item_addr: int) -> str:
    """从常见的若干偏移尝试读取 名称指针 并解码字符串"""
    candidate_offsets = [0x20, 0x24, 0x28, 0x2C, 0x30, 0x10, 0x14]
    for off in candidate_offsets:
        ptr = read_int(handle, item_addr + off)
        if is_plausible_ptr(ptr):
            name = read_string_gbk(handle, ptr, 64)
            if name and any(c.strip() for c in name):
                return name
    return ""


def try_extract_name_inline(handle, item_addr: int) -> str:
    """从常见的内嵌字符串偏移尝试读取名称"""
    candidate_inline = [
        (ITEM_NAME_INLINE_OFFSET, ITEM_NAME_MAXLEN),  # 精确偏移优先
        (0x2C, 16), (0x2C, 32), (0x40, 16), (0x40, 32), (0x60, 32)
    ]
    for off, size in candidate_inline:
        name = read_string_gbk(handle, item_addr + off, size)
        if name and any(c.strip() for c in name):
            return name
    return ""


def try_extract_count(handle, item_addr: int) -> int:
    """从固定偏移优先读取数量，失败时回退到常见偏移"""
    v = read_int(handle, item_addr + ITEM_COUNT_OFFSET)
    if 0 <= v < 1_000_000:
        return v
    candidate_offsets = [0x10, 0x14, 0x18, 0x1C, 0x34]
    best = 0
    for off in candidate_offsets:
        v = read_int(handle, item_addr + off)
        if 0 < v < 100000 and v > best:
            best = v
    return best


def try_extract_item_id(handle, item_addr: int) -> int:
    """从常见偏移尝试读取物品ID"""
    candidate_offsets = [0x00, 0x04, 0x08, 0x0C]
    for off in candidate_offsets:
        v = read_int(handle, item_addr + off)
        if 1 <= v <= 10_000_000:
            return v
    return 0


def read_item_info(handle, item_addr: int) -> dict:
    """读取单个物品信息（优先固定偏移，其次启发式）"""
    info = {
        'address': item_addr,
        'id': 0,
        'count': 0,
        'name': ''
    }
    try:
        if not is_plausible_ptr(item_addr):
            return info

        # 先按精确偏移读取内嵌名称
        name = read_string_gbk(handle, item_addr + ITEM_NAME_INLINE_OFFSET, ITEM_NAME_MAXLEN)
        if not name:
            # 再尝试指针名与其他内嵌候选
            name = try_extract_name_by_pointers(handle, item_addr)
            if not name:
                name = try_extract_name_inline(handle, item_addr)

        count = try_extract_count(handle, item_addr)
        item_id = try_extract_item_id(handle, item_addr)

        info['name'] = name
        info['count'] = count
        info['id'] = item_id
    except:  # noqa: E722
        pass
    return info


# ============================ 扫描逻辑 ============================

INVENTORY_BASE_ADDRESS = 0x0068FA10

# 固定偏移（来自IDA验证）
ITEM_NAME_INLINE_OFFSET = 0x18
ITEM_NAME_MAXLEN = 16
ITEM_COUNT_OFFSET = 0x431275C4


def detect_pointer_array(handle, base_addr: int, probe_slots: int = 40) -> bool:
    """探测是否为指针数组结构"""
    valid_ptrs = 0
    for i in range(probe_slots):
        slot_addr = base_addr + i * 4
        ok, value = test_memory_access(handle, slot_addr)
        if not ok:
            continue
        if is_plausible_ptr(value):
            valid_ptrs += 1
    return valid_ptrs >= max(3, probe_slots // 10)


def scan_inventory_pointer_array(handle, base_addr: int, max_slots: int = 100) -> list:
    """按 指针数组 方式扫描物品"""
    results = []
    for i in range(max_slots):
        slot_addr = base_addr + i * 4
        ok, item_ptr = test_memory_access(handle, slot_addr)
        if not ok:
            continue
        if is_plausible_ptr(item_ptr):
            item_info = read_item_info(handle, item_ptr)
            results.append({
                'slot': i,
                'slot_addr': slot_addr,
                'ptr': item_ptr,
                'item': item_info
            })
    return results


def scan_inventory_struct_array(handle, base_addr: int, struct_size_candidates=(0x20, 0x40, 0x60, 0x80), max_slots: int = 60) -> list:
    """按 结构体连续数组 方式扫描物品（尝试不同结构体大小）"""
    best_results = []
    best_named = -1
    for struct_size in struct_size_candidates:
        results = []
        named = 0
        for i in range(max_slots):
            item_addr = base_addr + i * struct_size
            ok, _ = test_memory_access(handle, item_addr)
            if not ok:
                continue
            info = read_item_info(handle, item_addr)
            if info['name']:
                named += 1
            results.append({
                'slot': i,
                'slot_addr': item_addr,
                'ptr': item_addr,
                'item': info,
                'struct_size': struct_size
            })
        if named > best_named:
            best_named = named
            best_results = results
    return best_results


def generate_inventory_list():
    """生成背包物品列表"""
    handle, pid = get_process_handle()
    if not handle:
        print("❌ 错误: 无法连接到游戏进程 mts.exe")
        print("请确保以管理员运行，且游戏已启动")
        return

    try:
        print("🎒 背包物品列表")
        print("=" * 60)
        print(f"进程 PID: {pid}")
        print(f"背包基址: 0x{INVENTORY_BASE_ADDRESS:08X}\n")

        # 探测结构形态
        is_ptr_array = detect_pointer_array(handle, INVENTORY_BASE_ADDRESS)
        print(f"结构判定: {'指针数组' if is_ptr_array else '结构体数组(猜测)'}\n")

        if is_ptr_array:
            results = scan_inventory_pointer_array(handle, INVENTORY_BASE_ADDRESS, 120)
        else:
            results = scan_inventory_struct_array(handle, INVENTORY_BASE_ADDRESS)

        # 展示
        print("槽位  槽位地址    物品地址    数量    ID        名称")
        print("-" * 80)

        shown = 0
        for r in results:
            slot = r['slot']
            slot_addr = r['slot_addr']
            item_ptr = r['ptr']
            info = r['item']
            count = info.get('count', 0)
            item_id = info.get('id', 0)
            name = info.get('name', '')
            name_display = name[:12] if name else ""
            if not (name or count or item_id):
                # 跳过完全无法解析的空槽，除非是前几个槽位用于提示
                if slot > 5:
                    continue
            print(f"{slot:2d}   0x{slot_addr:08X}  0x{item_ptr:08X}  {count:5d}  {item_id:8d}  {name_display}")
            shown += 1

        print(f"\n📊 共显示 {shown} 条记录")
        if shown == 0:
            print("⚠️ 未解析到任何物品，可能需要：")
            print("- 确认 0x0068FA10 是否为背包数组或首元素地址")
            print("- 在背包里放入至少一个物品并重新运行")
            print("- 如果你知道具体结构偏移，可告诉我以提升解析准确率")

    finally:
        ctypes.windll.kernel32.CloseHandle(handle)


def main():
    """主函数"""
    print("🎮 背包物品列表工具")
    print("=" * 40)
    print("功能: 扫描并列出背包内的物品")
    print()

    generate_inventory_list()


if __name__ == "__main__":
    main()


